
import React from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import Button from '../components/atoms/Button';
import EcosystemNetwork from '../components/interactive/EcosystemNetwork';
import AnticipatoryCursor from '../components/atoms/AnticipatoryCursor';
import ScrollDrivenText from '../components/atoms/ScrollDrivenText';
import {
  Globe,
  Network,
  Zap,
  Users,
  Heart,
  Microscope,
  ArrowRightLeft,
  Cog,
  Cpu,
  Shield,
  TrendingUp
} from 'lucide-react';

const Ecosystem: React.FC = () => {
  const navigate = useNavigate();

  const divisions = [
    {
      name: 'SymbioCore',
      tagline: 'The Heart of Intelligence',
      icon: Heart,
      color: 'consciousness',
      description: 'Cloud-based PaaS hosting the full suite of ACI models with robust API infrastructure. The central nervous system powering all divisions.',
      purpose: 'Democratizes access to dynamic, evolving AI through API subscriptions',
      link: '/symbiocore'
    },
    {
      name: 'SymbioLabs',
      tagline: 'The Engine of Innovation',
      icon: Microscope,
      color: 'creativity',
      description: 'Dedicated R&D division advancing ACI frontiers and developing next-generation nature-inspired AI models.',
      purpose: 'Fundamental research ensuring continuous technological leadership',
      link: '/symbiolabs'
    },
    {
      name: 'SymbioXchange',
      tagline: 'The Symbiosis Marketplace',
      icon: ArrowRightLeft,
      color: 'intuition',
      description: 'AI-driven B2B marketplace facilitating trading of resources, byproducts, and underutilized assets.',
      purpose: 'Transforms waste into tradable assets, creating true circular economy',
      link: '/symbioxchange'
    },
    {
      name: 'SymbioAutomate',
      tagline: 'The Efficiency Engine',
      icon: Cog,
      color: 'harmony',
      description: 'Enterprise-grade intelligent process automation creating dynamic, self-optimizing workflows.',
      purpose: 'Replaces rigid automation with fluid, intelligent adaptation',
      link: '/symbioautomate'
    },
    {
      name: 'SymbioEdge',
      tagline: 'Intelligence at the Frontier',
      icon: Cpu,
      color: 'transcendence',
      description: 'Lightweight ACI models on edge devices for real-time decision-making without cloud dependency.',
      purpose: 'Brings decentralized intelligence to the physical world',
      link: '/symbioedge'
    },
    {
      name: 'SymbioImpact',
      tagline: 'The Conscience of the Ecosystem',
      icon: Shield,
      color: 'consciousness',
      description: 'ESG technology and advisory using ACI to measure, verify, and report sustainability performance.',
      purpose: 'Transforms ESG from marketing to auditable business function',
      link: '/symbioimpact'
    },
    {
      name: 'SymbioVentures',
      tagline: 'The Catalyst for Growth',
      icon: TrendingUp,
      color: 'creativity',
      description: 'Corporate venture capital arm investing in early-stage startups in symbiotic technologies.',
      purpose: 'Self-fueling innovation engine strategically cultivating ecosystem',
      link: '/symbioventures'
    },
    {
      name: 'SymbioAlliance',
      tagline: 'The Global Scaling Engine',
      icon: Globe,
      color: 'intuition',
      description: 'Strategic partnerships for scaling symbiotic economy adoption through policy and industry standards.',
      purpose: 'Shapes entire markets, elevating symbiosis to global economic policy',
      link: '/symbioalliance'
    }
  ];

  const handleDivisionClick = (link: string) => {
    navigate(link);
  };

  const ecosystemNodes = [
    {
      id: 'symbioautomate',
      label: 'SymbioAutomate',
      size: 20,
      color: 'rgba(0, 255, 170, 0.8)',
      connections: ['symbiocore', 'symbiolabs', 'symbioxchange']
    },
    {
      id: 'symbiocore',
      label: 'SymbioCore',
      size: 25,
      color: 'rgba(0, 153, 255, 0.8)',
      connections: ['symbioautomate', 'symbiolabs', 'symbioedge']
    },
    {
      id: 'symbiolabs',
      label: 'SymbioLabs',
      size: 18,
      color: 'rgba(170, 0, 255, 0.8)',
      connections: ['symbiocore', 'symbioautomate', 'symbioventures']
    },
    {
      id: 'symbioxchange',
      label: 'SymbioXchange',
      size: 22,
      color: 'rgba(0, 255, 119, 0.8)',
      connections: ['symbioautomate', 'symbioedge', 'symbioimpact']
    },
    {
      id: 'symbioedge',
      label: 'SymbioEdge',
      size: 19,
      color: 'rgba(255, 170, 0, 0.8)',
      connections: ['symbiocore', 'symbioxchange', 'symbioalliance']
    },
    {
      id: 'symbioimpact',
      label: 'SymbioImpact',
      size: 21,
      color: 'rgba(255, 0, 170, 0.8)',
      connections: ['symbioxchange', 'symbioventures', 'symbioalliance']
    },
    {
      id: 'symbioventures',
      label: 'SymbioVentures',
      size: 17,
      color: 'rgba(0, 255, 255, 0.8)',
      connections: ['symbiolabs', 'symbioimpact', 'symbioalliance']
    },
    {
      id: 'symbioalliance',
      label: 'SymbioAlliance',
      size: 16,
      color: 'rgba(255, 255, 0, 0.8)',
      connections: ['symbioedge', 'symbioimpact', 'symbioventures']
    }
  ];

  return (
    <div className="min-h-screen surface-void">
      <Header />

      <main className="pt-24">
        <div className="container mx-auto px-8 py-16">
          <div className="text-center mb-16">
            <Typography as="h1" variant="4xl" weight="bold" gradient="consciousness" className="mb-6">
              SymbioWave Ecosystem
            </Typography>
            <Typography as="p" variant="xl" color="secondary" className="max-w-4xl mx-auto">
              A complete symbiotic platform where intelligence, automation, and human creativity converge.
            </Typography>
          </div>

          {/* Interactive Ecosystem Network */}
          <div className="mb-16">
            <Card variant="quantum" className="p-8 rounded-[32px] border-consciousness/25">
              <div className="text-center mb-8">
                <Typography variant="2xl" weight="semibold" color="consciousness" className="mb-4">
                  Interactive Ecosystem Network
                </Typography>
                <Typography variant="base" color="secondary" className="mb-6">
                  Explore the interconnected relationships between all SymbioWave divisions
                </Typography>
              </div>

              <div className="h-96 rounded-[20px] overflow-hidden">
                <EcosystemNetwork
                  nodes={ecosystemNodes}
                  isActive={true}
                  variant="force"
                />
              </div>
            </Card>
          </div>

          {/* Division Grid */}
          <div className="mb-16">
            <div className="text-center mb-12">
              <Typography
                as="h2"
                variant="3xl"
                weight="bold"
                gradient="consciousness"
                className="mb-4"
              >
                The Eight Divisions
              </Typography>
              <Typography
                variant="lg"
                color="secondary"
                className="max-w-3xl mx-auto"
              >
                Each division is a formidable business in its own right, yet their true power is unleashed when they work in synergy
              </Typography>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
              {divisions.map((division, index) => {
                const IconComponent = division.icon;
                return (
                  <AnticipatoryCursor
                    key={division.name}
                    intensity="medium"
                    glowColor={division.color as any}
                  >
                    <Card
                      variant="neural"
                      className={`group cursor-pointer transition-all duration-[600ms] hover:border-${division.color}/60 hover:transform hover:scale-105 hover:-translate-y-3 relative overflow-hidden`}
                      onClick={() => handleDivisionClick(division.link)}
                    >
                      <div className={`absolute inset-0 bg-gradient-to-br from-${division.color}/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-[600ms]`}></div>

                      <div className="relative z-10">
                        <div className={`w-20 h-20 mx-auto mb-6 rounded-cellular bg-${division.color}/10 flex items-center justify-center group-hover:bg-${division.color}/20 transition-all duration-[400ms] group-hover:animate-cellular-flow group-hover:rotate-12`}>
                          <IconComponent className={`w-10 h-10 text-${division.color} group-hover:scale-110 transition-all duration-[400ms] group-hover:drop-shadow-lg`} />
                        </div>

                        <Typography
                          variant="lg"
                          weight="bold"
                          className={`mb-2 text-${division.color} text-center group-hover:animate-pulse`}
                        >
                          {division.name}
                        </Typography>

                        <Typography
                          variant="sm"
                          color="secondary"
                          className="mb-4 text-center italic group-hover:text-consciousness transition-colors duration-[400ms]"
                        >
                          {division.tagline}
                        </Typography>

                        <Typography
                          variant="xs"
                          color="tertiary"
                          className="mb-4 leading-relaxed text-center"
                        >
                          {division.description}
                        </Typography>

                        <div className={`p-3 glass-quantum rounded-lg border border-${division.color}/20 group-hover:border-${division.color}/40 transition-all duration-[400ms] group-hover:backdrop-blur-xl`}>
                          <Typography
                            variant="micro"
                            weight="medium"
                            className={`text-${division.color} text-center`}
                          >
                            {division.purpose}
                          </Typography>
                        </div>
                      </div>

                      <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-[400ms] group-hover:rotate-45">
                        <div className={`w-6 h-6 rounded-full bg-${division.color}/20 flex items-center justify-center backdrop-blur-sm`}>
                          <div className={`w-3 h-3 border-r-2 border-t-2 border-${division.color} transform rotate-45`}></div>
                        </div>
                      </div>
                    </Card>
                  </AnticipatoryCursor>
                );
              })}
            </div>

            <AnticipatoryCursor intensity="medium" glowColor="consciousness">
              <Card variant="quantum" className="text-center border-consciousness/30 mb-8 hover:border-consciousness/60 transition-all duration-[600ms] hover:backdrop-blur-xl">
                <Typography
                  variant="xl"
                  className="mb-6 font-bold bg-gradient-to-r from-consciousness-500 via-creativity-500 to-harmony-500 bg-clip-text text-transparent"
                >
                  The Power of Integration
                </Typography>

                <Typography
                  variant="lg"
                  color="secondary"
                  className="mb-8 max-w-3xl mx-auto leading-relaxed"
                >
                  Each division amplifies the others. SymbioCore powers SymbioXchange's matching algorithms. SymbioAutomate orchestrates transactions. SymbioImpact validates sustainability gains. Together, they create an unbeatable competitive moat.
                </Typography>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <AnticipatoryCursor intensity="intense" glowColor="consciousness">
                    <Button
                      variant="quantum"
                      size="lg"
                      onClick={() => navigate('/ecosystem-flywheel')}
                      className="hover:scale-105 transition-transform duration-[400ms]"
                    >
                      See the Synergistic Flywheel
                    </Button>
                  </AnticipatoryCursor>
                  <AnticipatoryCursor intensity="medium" glowColor="harmony">
                    <Button
                      variant="outline-quantum"
                      size="lg"
                      onClick={() => navigate('/symbioautomate')}
                      className="hover:scale-105 transition-transform duration-[400ms]"
                    >
                      Explore SymbioAutomate
                    </Button>
                  </AnticipatoryCursor>
                </div>
              </Card>
            </AnticipatoryCursor>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-16">
            <Card variant="consciousness" className="p-10 rounded-[32px] border-consciousness/25">
              <Globe className="w-20 h-20 mb-8 text-consciousness" />
              <Typography variant="xl" weight="semibold" color="consciousness" className="mb-6">
                Platform Overview
              </Typography>
              <Typography variant="base" color="secondary" className="leading-relaxed">
                Our ecosystem connects 8 specialized divisions, each optimized for specific challenges while 
                maintaining perfect symbiosis with the whole system.
              </Typography>
            </Card>

            <Card variant="creativity" className="p-10 rounded-[32px] border-creativity/25">
              <Network className="w-20 h-20 mb-8 text-creativity" />
              <Typography variant="xl" weight="semibold" color="creativity" className="mb-6">
                Integration Layer
              </Typography>
              <Typography variant="base" color="secondary" className="leading-relaxed">
                Seamless connectivity between all divisions ensures that insights from one area 
                enhance capabilities across the entire ecosystem.
              </Typography>
            </Card>

            <Card variant="intuition" className="p-10 rounded-[32px] border-harmony/25">
              <Zap className="w-20 h-20 mb-8 text-harmony" />
              <Typography variant="xl" weight="semibold" color="harmony" className="mb-6">
                Real-time Intelligence
              </Typography>
              <Typography variant="base" color="secondary" className="leading-relaxed">
                Live data flows create continuous optimization loops that adapt to changing 
                conditions without human intervention.
              </Typography>
            </Card>

            <Card variant="neural" className="p-10 rounded-[32px] border-consciousness/25">
              <Users className="w-20 h-20 mb-8 text-consciousness" />
              <Typography variant="xl" weight="semibold" color="consciousness" className="mb-6">
                Network Effects
              </Typography>
              <Typography variant="base" color="secondary" className="leading-relaxed">
                The value of each division multiplies as more participants join, creating 
                exponential benefits for all ecosystem members.
              </Typography>
            </Card>
          </div>

          <div className="glass-quantum rounded-[32px] p-12 border border-consciousness/30">
            <Typography as="h2" variant="2xl" weight="semibold" color="consciousness" className="mb-8 text-center">
              The Power of Symbiosis
            </Typography>
            <Typography variant="lg" color="secondary" className="leading-relaxed text-center max-w-4xl mx-auto">
              Unlike isolated solutions, our ecosystem creates emergent intelligence that grows stronger 
              with each interaction. Every division contributes to and benefits from the collective wisdom 
              of the entire network.
            </Typography>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Ecosystem;
